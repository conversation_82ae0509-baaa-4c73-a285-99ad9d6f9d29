import { useAccountTranslation } from '@core/hooks/useTranslation'
import { useFeatureFlag } from '@core/redux/features/featureFlags'
import { FeatureFlag } from '@core/ssr/featureFlag/featureFlag.enum'

import { ReturnLabelActions } from '@modules/account/components/orders-history/components/return-label-actions/ReturnLabelActions'
import { ContactSellerButton } from '@modules/message-center/components/ContactSeller/ContactSellerButton'
import { BUTTON_VARIANTS } from '@modules/shared/components/Button/types/variant'

import { OrderLine } from '../../types'
import { BuyAgainButton } from '../buy-again-button/BuyAgainButton'
import { OrderHistoryRowDesktop } from '../order-history-row-desktop/OrderHistoryRowDesktop'
import { OrderLineStatus } from '../order-line-status/OrderLineStatus'
import { OrderTrackItems } from '../order-track-items/OrderTrackItems'
import { OrdersHistoryOrderLineDescription } from '../orders-history-order-line-description/OrdersHistoryOrderLineDescription'

interface Props {
  orderLine: OrderLine
  orderId: string
  orderNumber: string
  showContactSellerButton: boolean
}

export const OrdersHistoryOrderLine = ({
  orderLine,
  orderId,
  orderNumber,
  showContactSellerButton
}: Props) => {
  const { t } = useAccountTranslation()
  const isMessageCenterC2AFeatureEnabled = useFeatureFlag(
    FeatureFlag.FF_CCS_MESSAGE_CENTER_ORDER_C2A
  )

  return (
    <section
      className="lg:last:rounded-b-[2px] lg:border-b-[1px] lg:border-l-[1px] lg:border-r-[1px] lg:border-[#E5E7EA] sm:my-12px lg:my-0 lg:p-[16px] w-full space-y-3"
      data-testid="orders-history-order-line"
    >
      <div className="hidden lg:block">
        <OrderHistoryRowDesktop
          firstColumn={
            <OrdersHistoryOrderLineDescription orderLine={orderLine} />
          }
          secondColumn={
            <OrderLineStatus orderLine={orderLine} className="flex-col" />
          }
          thirdColumn={
            <div className="flex flex-col space-y-3">
              {showContactSellerButton && isMessageCenterC2AFeatureEnabled && (
                <ContactSellerButton
                  orderId={orderId}
                  orderNumber={orderNumber}
                  orderLine={orderLine}
                >
                  {t(
                    'PAGES.ACCOUNT.ORDER_HISTORY.ORDER_DETAIL.ORDER_LINE.SEE_MESSAGES'
                  )}
                </ContactSellerButton>
              )}
              <OrderTrackItems
                orderLine={orderLine}
                variant={BUTTON_VARIANTS.infoAccessible}
              />
              {orderLine.isBuyAgain && (
                <BuyAgainButton
                  orderLine={orderLine}
                  defaultButtonVariant={BUTTON_VARIANTS.infoAccessible}
                />
              )}
              <ReturnLabelActions orderLine={orderLine} isHighlighted={false} />
            </div>
          }
        />
      </div>

      <div className="lg:hidden w-full space-y-3">
        <div className="space-y-6">
          <OrderLineStatus
            orderLine={orderLine}
            className="flex-col"
            showHeader
          />
          <OrdersHistoryOrderLineDescription orderLine={orderLine} />
        </div>
        <div className="w-full space-y-3">
          <OrderTrackItems
            orderLine={orderLine}
            variant={BUTTON_VARIANTS.infoAccessible}
          />
          {orderLine.isBuyAgain && (
            <BuyAgainButton
              orderLine={orderLine}
              defaultButtonVariant={BUTTON_VARIANTS.infoAccessible}
            />
          )}
          <ReturnLabelActions orderLine={orderLine} isHighlighted={false} />
        </div>
      </div>
    </section>
  )
}
